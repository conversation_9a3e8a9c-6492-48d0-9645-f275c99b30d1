{"name": "braces", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "version": "3.0.3", "homepage": "https://github.com/micromatch/braces", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (https://twitter.com/doowb)", "<PERSON><PERSON> (https://github.com/es128)", "<PERSON> (https://github.com/eush77)", "hemanth.hm (http://h3manth.com)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)"], "repository": "micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "dependencies": {"fill-range": "^7.1.1"}, "devDependencies": {"ansi-colors": "^3.2.4", "bash-path": "^2.0.1", "gulp-format-md": "^2.0.0", "mocha": "^6.1.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "lint": {"reflinks": true}, "plugins": ["gulp-format-md"]}}